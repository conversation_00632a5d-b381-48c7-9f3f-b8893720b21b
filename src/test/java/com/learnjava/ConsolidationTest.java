package com.learnjava;


import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Arrays;
import java.util.List;

public class ConsolidationTest {

    @Test
    public void testFizzBuzzList() {
        int n = 10;
        List<String> expected = Arrays.asList("1", "2", "fizz", "4", "buzz", "fizz", "7", "8", "fizz", "buzz");
        List<String> result = Consolidation.fizzBuzzList(n);
        assertEquals(expected, result);
    }

    @Test
    public void testFizzBuzzSingle() {
        //regular numbers
        assertEquals("1", Consolidation.fizzBuzzSingle(1));
        assertEquals("2", Consolidation.fizzBuzzSingle(2));
        assertEquals("4", Consolidation.fizzBuzzSingle(4));
        assertEquals("7", Consolidation.fizzBuzzSingle(7));
        assertEquals("8", Consolidation.fizzBuzzSingle(8));

        //multiples of 3
        assertEquals("fizz", Consolidation.fizzBuzzSingle(3));
        assertEquals("fizz", Consolidation.fizzBuzzSingle(6));
        assertEquals("fizz", Consolidation.fizzBuzzSingle(9));

        //multiples of 5
        assertEquals("buzz", Consolidation.fizzBuzzSingle(5));
        assertEquals("buzz", Consolidation.fizzBuzzSingle(10));
        assertEquals("buzz", Consolidation.fizzBuzzSingle(20));

        //multiples of 3 and 5
        assertEquals("fizzbuzz", Consolidation.fizzBuzzSingle(15));
        assertEquals("fizzbuzz", Consolidation.fizzBuzzSingle(30));
        assertEquals("fizzbuzz", Consolidation.fizzBuzzSingle(45));
    }
}
