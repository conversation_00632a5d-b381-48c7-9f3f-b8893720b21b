package com.learnjava;


import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;


public class ConsolidationTest {

    @Test
    public void testFizzBuzzList() {
        int n = 10;
        List<String> expected = Arrays.asList("1", "2", "fizz", "4", "buzz", "fizz", "7", "8", "fizz", "buzz");
        List<String> result = Consolidation.fizzBuzzList(n);
        assertEquals(expected, result);
    }

    @Test
    public void testFizzBuzzSingle() {
        //regular numbers
        assertEquals("1", Consolidation.fizzBuzzSingle(1));
        assertEquals("2", Consolidation.fizzBuzzSingle(2));
        assertEquals("4", Consolidation.fizzBuzzSingle(4));
        assertEquals("7", Consolidation.fizzBuzzSingle(7));
        assertEquals("8", Consolidation.fizzBuzzSingle(8));

        //multiples of 3
        assertEquals("fizz", Consolidation.fizzBuzzSingle(3));
        assertEquals("fizz", Consolidation.fizzBuzzSingle(6));
        assertEquals("fizz", Consolidation.fizzBuzzSingle(9));

        //multiples of 5
        assertEquals("buzz", Consolidation.fizzBuzzSingle(5));
        assertEquals("buzz", Consolidation.fizzBuzzSingle(10));
        assertEquals("buzz", Consolidation.fizzBuzzSingle(20));

        //multiples of 3 and 5
        assertEquals("fizzbuzz", Consolidation.fizzBuzzSingle(15));
        assertEquals("fizzbuzz", Consolidation.fizzBuzzSingle(30));
        assertEquals("fizzbuzz", Consolidation.fizzBuzzSingle(45));
    }

    @Test
    public void testFibonaci() {
        assertEquals(0, Consolidation.fibonacci(0));
        assertEquals(1, Consolidation.fibonacci(1));
        assertEquals(1, Consolidation.fibonacci(2));
        assertEquals(2, Consolidation.fibonacci(3));
        assertEquals(3, Consolidation.fibonacci(4));
        assertEquals(5, Consolidation.fibonacci(5));
        assertEquals(8, Consolidation.fibonacci(6));
    }

    @Test
    public void testFactorial() {
        assertEquals(1, Consolidation.factorial(0));
        assertEquals(1, Consolidation.factorial(1));
        assertEquals(2, Consolidation.factorial(2));
        assertEquals(6, Consolidation.factorial(3));
        assertEquals(24, Consolidation.factorial(4));
        assertEquals(120, Consolidation.factorial(5));
    }

    @Test
    public void testCheckNumberOddNumber() {
        // Tests for positive odd numbers - should return "Weird"
        assertEquals("Weird", Consolidation.checkNumber(1));
        assertEquals("Weird", Consolidation.checkNumber(3));
        assertEquals("Weird", Consolidation.checkNumber(7));
        assertEquals("Weird", Consolidation.checkNumber(15));
        assertEquals("Weird", Consolidation.checkNumber(21));
    }

    @Test
    public void testCheckNumberEvenRange2To5() {
        // Tests for even numbers in range 2 to 5 - should return "Not Weird"
        assertEquals("Not Weird", Consolidation.checkNumber(2));
        assertEquals("Not Weird", Consolidation.checkNumber(4));
    }

    @Test
    public void testCheckNumberEvenRange6To20() {
        // Tests for even numbers in range 6 to 20 - should return "Weird"
        assertEquals("Weird", Consolidation.checkNumber(6));
        assertEquals("Weird", Consolidation.checkNumber(8));
        assertEquals("Weird", Consolidation.checkNumber(10));
        assertEquals("Weird", Consolidation.checkNumber(12));
        assertEquals("Weird", Consolidation.checkNumber(14));
        assertEquals("Weird", Consolidation.checkNumber(16));
        assertEquals("Weird", Consolidation.checkNumber(18));
        assertEquals("Weird", Consolidation.checkNumber(20));
    }

    @Test
    public void testCheckNumberEvenGreaterThan20() {
        // Tests for even numbers greater than 20 - should return "Not Weird"
        assertEquals("Not Weird", Consolidation.checkNumber(22));
        assertEquals("Not Weird", Consolidation.checkNumber(24));
        assertEquals("Not Weird", Consolidation.checkNumber(100));
    }

    @Test
    public void testCheckNumberNegativeEvenNumber() {
        // Tests for non-positive even numbers - should return "Very weird"
        assertEquals("Very weird", Consolidation.checkNumber(-2));
        assertEquals("Very weird", Consolidation.checkNumber(-4));
        assertEquals("Very weird", Consolidation.checkNumber(-10));
    }

    @Test
    public void testCheckNumberNegativeOddNumber() {
        // Tests for non-positive odd numbers - should return "Extremely Weird"
        assertEquals("Extremely Weird", Consolidation.checkNumber(-1));
        assertEquals("Extremely Weird", Consolidation.checkNumber(-3));
        assertEquals("Extremely Weird", Consolidation.checkNumber(-7));
    }

    @Test
    public void testCheckNumberNeutral() {
        // Tests for numbers that are neutral (0) - should return "Neutral"
        assertEquals("Neutral", Consolidation.checkNumber(0));
    }
}
