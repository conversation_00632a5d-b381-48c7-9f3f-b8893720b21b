package com.learnjava;


import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

public class ConsolidationTest {

    @Test
    public void testFizzBuzzList() {
        int n = 10;
        List<String> expected = Arrays.asList("1", "2", "fizz", "4", "buzz", "fizz", "7", "8", "fizz", "buzz");
        List<String> result = Consolidation.fizzBuzzList(n);
        assert result.equals(expected);
    }
}
