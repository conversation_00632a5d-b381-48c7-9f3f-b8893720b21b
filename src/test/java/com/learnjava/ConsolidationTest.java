package com.learnjava;


import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

public class ConsolidationTest {

    @Test
    public void testFizzBuzzList() {
        int n = 10;
        List<String> expected = Arrays.asList("1", "2", "fizz", "4", "buzz", "fizz", "7", "8", "fizz", "buzz");
        List<String> result = Consolidation.fizzBuzzList(n);
        assertEquals(expected, result);
    }

    @Test
    public void testFizzBuzzsingle() {
        //reular numbers
        assertEquals("1", Consolidation.fizzBuzz(1));
        assertEquals("2", Consolidation.fizzBuzz(2));
        assertEquals("4", Consolidation.fizzBuzz(4));
        assertEquals("7", Consolidation.fizzBuzz(7));
        assertEquals("8", Consolidation.fizzBuzz(8));

        //multiples of 3
        assertEquals("fizz", Consolidation.fizzBuzz(3));
        assertEquals("fizz", Consolidation.fizzBuzz(6));
        assertEquals("fizz", Consolidation.fizzBuzz(9));

        //multiples of 5
        assertEquals("buzz", Consolidation.fizzBuzz(5));
        assertEquals("buzz", Consolidation.fizzBuzz(10));
        assertEquals("buzz", Consolidation.fizzBuzz(20));

        //multiples of 3 and 5
        assertEquals("fizzbuzz", Consolidation.fizzBuzz(15));
        assertEquals("fizzbuzz", Consolidation.fizzBuzz(30));
        assertEquals("fizzbuzz", Consolidation.fizzBuzz(45));
    }
}
