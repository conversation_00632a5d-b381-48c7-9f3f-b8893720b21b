package com.learnjava;

import java.util.ArrayList;
import java.util.List;

public class Consolidation {

    /**
     * FizzBuzz implementation
     * @param n the upper limit (inclusive)
     * @return list of strings with fizz/buzz/fizzbuzz logic
     */
    public static List<String> fizzBuzzList(int n) {
        List<String> result = new ArrayList<>(n); //empty list to store results of fizzbuzz

        for (int i = 1; i <= n; i++) {
            if (i % 15 == 0) {  // Multiple of both 3 and 5
                result.add("fizzbuzz");
            } else if (i % 3 == 0) {  // Multiple of 3
                result.add("fizz");
            } else if (i % 5 == 0) {  // Multiple of 5
                result.add("buzz");
            } else {
                result.add(String.valueOf(i));
            }
        }

        return result;
    }

    /**
     * FizzBuzz implementation for a single number
     * @param i the number to check
     * @return "fizzbuzz" for multiples of 15, "fizz" for multiples of 3, "buzz" for multiples of 5, or the number as string
     */
    public static String fizzBuzzSingle(int i) {
        if (i % 15 == 0) {  // Multiple of both 3 and 5
            return "fizzbuzz";
        } else if (i % 3 == 0) {  // Multiple of 3
            return "fizz";
        } else if (i % 5 == 0) {  // Multiple of 5
            return "buzz";
        } else {
            return String.valueOf(i);
        }
    }
}
