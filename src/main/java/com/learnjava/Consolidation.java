package com.learnjava;

import java.util.ArrayList;
import java.util.List;

public class Consolidation {

    /**
     * FizzBuzz implementation
     * @param n the upper limit (inclusive)
     * @return list of strings with fizz/buzz/fizzbuzz logic
     */
    public static List<String> fizzBuzzList(int n) {
        List<String> result = new ArrayList<>(n); //empty list to store results of fizzbuzz

        for (int i = 1; i <= n; i++) {
            if (i % 15 == 0) {  // Multiple of both 3 and 5
                result.add("fizzbuzz");
            } else if (i % 3 == 0) {  // Multiple of 3
                result.add("fizz");
            } else if (i % 5 == 0) {  // Multiple of 5
                result.add("buzz");
            } else {
                result.add(String.valueOf(i));
            }
        }

        return result;
    }

    /**
     * FizzBuzz implementation for a single number
     * @param i the number to check
     * @return "fizzbuzz" for multiples of 15, "fizz" for multiples of 3, "buzz" for multiples of 5, or the number as string
     */
    public static String fizzBuzzSingle(int i) {
        if (i % 15 == 0) {  // Multiple of both 3 and 5
            return "fizzbuzz";
        } else if (i % 3 == 0) {  // Multiple of 3
            return "fizz";
        } else if (i % 5 == 0) {  // Multiple of 5
            return "buzz";
        } else {
            return String.valueOf(i);
        }
    }

    public static int fibonacci(int i) {
        if (i == 0) {
            return 0;
        }

        if (i == 1) {
            return 1;
        }

        return fibonacci(i - 1) + fibonacci(i - 2);
    }

    public static int factorial(int n) {
        if (n == 0) {
            return 1;
        }

        return n * factorial(n - 1);
    }

    /**
     * Check number based on specific conditions
     * @param n the number to check
     * @return appropriate string based on the number's properties
     */
    public static String checkNumber(int n) {
        // Check for neutral (zero)
        if (n == 0) {
            return "Neutral";
        }

        // Check for negative numbers
        if (n < 0) {
            if (n % 2 == 0) {  // Negative even
                return "Very weird";
            } else {  // Negative odd
                return "Extremely Weird";
            }
        }

        // Positive numbers
        if (n % 2 == 1) {  // Positive odd
            return "Weird";
        } else {  // Positive even
            if (n >= 2 && n <= 5) {
                return "Not Weird";
            } else if (n >= 6 && n <= 20) {
                return "Weird";
            } else {  // n > 20
                return "Not Weird";
            }
        }
    }

    /**
     * Extract the date of birth from South African ID number
     * @param idNumber the 13-digit ID number as string
     * @return date of birth in DD/MM/YY format
     */
    public static String getDateOfBirth(String idNumber) {
        // Extract: YYMMDD from positions 0-5, rearrange to DD/MM/YY
        String day = idNumber.substring(4, 6);
        String month = idNumber.substring(2, 4);
        String year = idNumber.substring(0, 2);
        return day + "/" + month + "/" + year;
    }

    /**
     * Extract gender from South African ID number
     * @param idNumber the 13-digit ID number as string
     * @return "Male" or "Female"
     */
    public static String getGender(String idNumber) {
        // Extract digits 7-10 (positions 6-9 in 0-based indexing)
        int genderDigits = Integer.parseInt(idNumber.substring(6, 10));

        if (genderDigits < 5000) {
            return "Female";
        } else {
            return "Male";
        }
    }

    /**
     * Extract citizenship from South African ID number
     * @param idNumber the 13-digit ID number as string
     * @return "South African" or "Non-South African"
     */
    public static String getCitizenship(String idNumber) {
        // Extract digits 11-12 (positions 10-11 in 0-based indexing)
        int citizenshipDigits = Integer.parseInt(idNumber.substring(10, 12));

        if (citizenshipDigits < 1) {
            return "South African";
        } else {
            return "Non-South African";
        }
    }

    /**
     * Check if a string is a palindrome (reads the same forwards and backwards)
     * @param str the string to check
     * @return true if the string is a palindrome, false otherwise
     */
    public static boolean isPalindrome(String str) {
        // Handle null input
        if (str == null) {
            return false;
        }

        // Convert to lowercase for case-insensitive comparison (optional)
        // Remove this line if you want case-sensitive comparison
        // str = str.toLowerCase();

        int left = 0;
        int right = str.length() - 1;

        while (left < right) {
            if (str.charAt(left) != str.charAt(right)) {
                return false;
            }
            left++;
            right--;
        }

        return true;
    }
}
